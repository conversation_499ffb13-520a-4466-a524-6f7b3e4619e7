{"type": "module", "name": "data_relationship_analysis_agent", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"start": "node src/index.js", "sql": "node src/sqlGenerator.js", "debug": "node --inspect src/index.js", "debug-brk": "node --inspect-brk src/index.js", "api": "node src/api/app.js", "api:dev": "nodemon src/api/app.js", "build": "npm install --production", "prod": "NODE_ENV=production node src/api/app.js", "prod:pm2": "pm2 start src/api/app.js --name data-relationship-api", "docker:build": "docker build --platform linux/amd64 -t data-relationship-analysis-agent .", "docker:run": "docker run --platform linux/amd64 -p 3000:3000 data-relationship-analysis-agent", "docker:save": "docker save data-relationship-analysis-agent:latest -o data-relationship-analysis-agent.tar", "docker:load": "docker load -i data-relationship-analysis-agent.tar"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@langchain/anthropic": "^0.3.21", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@langchain/deepseek": "^0.0.2", "@langchain/openai": "^0.5.15", "axios": "^1.9.0", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "express": "^5.1.0", "ioredis": "^5.6.1", "langchain": "^0.3.28", "multer": "^2.0.1", "neo4j-driver": "^5.5.0", "openai": "^5.10.1", "oracledb": "^6.8.0", "pdf-parse": "^1.1.1", "pdf-table-extractor": "^1.0.3", "redis": "^4.7.1", "typeorm": "^0.3.23"}, "devDependencies": {"nodemon": "^3.0.3"}}