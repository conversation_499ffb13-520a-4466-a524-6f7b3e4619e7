import oracledb from 'oracledb';
import { v4 as uuidv4 } from 'uuid';
import DatabaseConfig from '../config/database.js';

class MetricTool{
    constructor() {
        this.config = new DatabaseConfig();
    }

    // 工具函数：获取Oracle连接
    async getConnection() {
        return await oracledb.getConnection(this.config.getOracleMetricConfig());
    }

     //保存指标记录
    async saveMetric(metricList) {
        const createTime = new Date();
        let conn;
        try {
            let count = 0;
            conn = await this.getConnection();
            for (const metric of metricList) {
              if(metric && typeof metric === 'object'){
                //新增ID
                const id = uuidv4().replace(/-/g, '');
                //拼接SQL
                await conn.execute(
                    `INSERT INTO METRIC (METRIC_ID, METRIC_NAME, METRIC_DEFINE, METRIC_NUMERATOR, METRIC_DENOMINATOR,
                    METRIC_CHAPTER,METRIC_ONE,METRIC_TWO,METRIC_THREE,METRIC_FOUR,DELETE_FLAG,METRIC_DATA_SOURCE,METRIC_TYPE,METRIC_FORMULA,
                    STANDARD_NAME,VERSION,PROVINCE,CREATE_USER, CREATE_TIME,UPDATE_TIME)
                    VALUES (:METRIC_ID, :METRIC_NAME, :METRIC_DEFINE, :METRIC_NUMERATOR, :METRIC_DENOMINATOR,
                    :METRIC_CHAPTER, :METRIC_ONE,:METRIC_TWO,:METRIC_THREE,:METRIC_FOUR,:DELETE_FLAG,:METRIC_DATA_SOURCE,:METRIC_TYPE,:METRIC_FORMULA,
                    :STANDARD_NAME,:VERSION,:PROVINCE,
                    :CREATE_USER ,:CREATE_TIME,:UPDATE_TIME)`,
                    { 
                        METRIC_ID: id,
                        METRIC_NAME: metric.METRIC_NAME || '',
                        METRIC_DEFINE: metric.METRIC_DEFINE || '',
                        METRIC_NUMERATOR: metric.METRIC_NUMERATOR || '',
                        METRIC_DENOMINATOR: metric.METRIC_DENOMINATOR || '',
                        METRIC_CHAPTER: metric.METRIC_CHAPTER || '',
                        METRIC_ONE: metric.METRIC_ONE || '',
                        METRIC_TWO: metric.METRIC_TWO || '',
                        METRIC_THREE: metric.METRIC_THREE || '',
                        METRIC_FOUR: metric.METRIC_FOUR || '',
                        DELETE_FLAG: 0,
                        METRIC_DATA_SOURCE: metric.METRIC_SOURCE || '标准文件导入',
                        METRIC_TYPE: '分子|分母',
                        METRIC_FORMULA: metric.METRIC_NUMERATOR + " / " + metric.METRIC_DENOMINATOR || '',
                        STANDARD_NAME: metric.STANDARD_NAME || '',
                        VERSION: metric.VERSION || '',
                        PROVINCE: metric.PROVINCE || '',
                        CREATE_USER: 'admin',
                        CREATE_TIME: createTime,
                        UPDATE_TIME: createTime
                    },
                    { autoCommit: false }
                );

                // const idTwo = uuidv4().replace(/-/g, '');
                // await conn.execute(
                //     `INSERT INTO METRIC (METRIC_ID, METRIC_NAME, METRIC_DEFINE, METRIC_NUMERATOR, METRIC_DENOMINATOR,
                //     METRIC_CHAPTER,METRIC_ONE,METRIC_TWO,METRIC_THREE,METRIC_FOUR,DELETE_FLAG,METRIC_DATA_SOURCE,METRIC_TYPE,METRIC_FORMULA,CREATE_USER, CREATE_TIME,UPDATE_TIME)
                //     VALUES (:METRIC_ID, :METRIC_NAME, :METRIC_DEFINE, :METRIC_NUMERATOR, :METRIC_DENOMINATOR,
                //     :METRIC_CHAPTER, :METRIC_ONE,:METRIC_TWO,:METRIC_THREE,:METRIC_FOUR,:DELETE_FLAG,:METRIC_DATA_SOURCE,:METRIC_TYPE,:METRIC_FORMULA,:CREATE_USER ,:CREATE_TIME,:UPDATE_TIME)`,
                //     { 
                //         METRIC_ID: idTwo,
                //         METRIC_NAME: metric.METRIC_NAME || '',
                //         METRIC_DEFINE: metric.METRIC_DEFINE || '',
                //         METRIC_NUMERATOR: metric.METRIC_NUMERATOR || '',
                //         METRIC_DENOMINATOR: metric.METRIC_DENOMINATOR || '',
                //         METRIC_CHAPTER: metric.METRIC_CHAPTER || '',
                //         METRIC_ONE: metric.METRIC_ONE || '',
                //         METRIC_TWO: metric.METRIC_TWO || '',
                //         METRIC_THREE: metric.METRIC_THREE || '',
                //         METRIC_FOUR: metric.METRIC_FOUR || '',
                //         DELETE_FLAG: 0,
                //         METRIC_DATA_SOURCE: metric.METRIC_DATA_SOURCE || '',
                //         METRIC_TYPE: '分母',
                //         METRIC_FORMULA: metric.METRIC_DENOMINATOR || '',
                //         CREATE_USER: 'admin',
                //         CREATE_TIME: createTime,
                //         UPDATE_TIME: createTime
                //     },
                //     { autoCommit: false }
                // );
                count++;
              }
            }
            await conn.commit();
            return "成功入库"+ count +"条指标！";
        } catch (err) {
            console.log("保存失败：", err.message);
            return null;
        } finally {
            if (conn) await conn.close();
        }
    }

    // 按条件查询指标记录
    async queryMetrics(conditions = {}) {
        let conn;
        try {
            conn = await this.getConnection();

            // 构建基础查询SQL
            let sql = `SELECT METRIC_ID, METRIC_NAME, METRIC_DEFINE, METRIC_NUMERATOR, METRIC_DENOMINATOR,
                       METRIC_CHAPTER, METRIC_ONE, METRIC_TWO, METRIC_THREE, METRIC_FOUR, DELETE_FLAG,
                       METRIC_DATA_SOURCE, METRIC_TYPE, METRIC_FORMULA, STANDARD_NAME, VERSION, PROVINCE,
                       CREATE_USER, CREATE_TIME, UPDATE_TIME
                       FROM METRIC WHERE DELETE_FLAG = 0`;

            const params = {};
            const whereClauses = [];

            // 动态构建WHERE条件
            if (conditions.metricName) {
                whereClauses.push('METRIC_NAME LIKE :metricName');
                params.metricName = `%${conditions.metricName}%`;
            }

            if (conditions.metricChapter) {
                whereClauses.push('METRIC_CHAPTER = :metricChapter');
                params.metricChapter = conditions.metricChapter;
            }

            if (conditions.metricOne) {
                whereClauses.push('METRIC_ONE = :metricOne');
                params.metricOne = conditions.metricOne;
            }

            if (conditions.metricTwo) {
                whereClauses.push('METRIC_TWO = :metricTwo');
                params.metricTwo = conditions.metricTwo;
            }

            if (conditions.metricThree) {
                whereClauses.push('METRIC_THREE = :metricThree');
                params.metricThree = conditions.metricThree;
            }

            if (conditions.metricFour) {
                whereClauses.push('METRIC_FOUR = :metricFour');
                params.metricFour = conditions.metricFour;
            }

            if (conditions.metricType) {
                whereClauses.push('METRIC_TYPE = :metricType');
                params.metricType = conditions.metricType;
            }

            if (conditions.standardName) {
                whereClauses.push('STANDARD_NAME LIKE :standardName');
                params.standardName = `%${conditions.standardName}%`;
            }

            if (conditions.version) {
                whereClauses.push('VERSION = :version');
                params.version = conditions.version;
            }

            if (conditions.province) {
                whereClauses.push('PROVINCE = :province');
                params.province = conditions.province;
            }

            if (conditions.metricDataSource) {
                whereClauses.push('METRIC_DATA_SOURCE = :metricDataSource');
                params.metricDataSource = conditions.metricDataSource;
            }

            // 添加WHERE条件到SQL
            if (whereClauses.length > 0) {
                sql += ' AND ' + whereClauses.join(' AND ');
            }

            // 添加排序
            sql += ' ORDER BY CREATE_TIME DESC';

            // 添加分页支持
            if (conditions.pageSize && conditions.pageNum) {
                const offset = (conditions.pageNum - 1) * conditions.pageSize;
                sql = `SELECT * FROM (
                    SELECT ROWNUM rn, t.* FROM (${sql}) t
                    WHERE ROWNUM <= :endRow
                ) WHERE rn > :startRow`;
                params.startRow = offset;
                params.endRow = offset + conditions.pageSize;
            }

            console.log('执行查询SQL:', sql);
            console.log('查询参数:', params);

            const result = await conn.execute(sql, params);

            // 转换结果为对象数组
            const metrics = result.rows.map(row => {
                const metric = {};
                result.metaData.forEach((col, index) => {
                    metric[col.name] = row[index];
                });
                return metric;
            });

            return {
                success: true,
                data: metrics,
                total: metrics.length,
                message: `查询成功，共找到 ${metrics.length} 条记录`
            };

        } catch (err) {
            console.log("查询失败：", err.message);
            return {
                success: false,
                data: [],
                total: 0,
                message: `查询失败：${err.message}`
            };
        } finally {
            if (conn) await conn.close();
        }
    }

    // 根据ID查询单个指标
    async getMetricById(metricId) {
        let conn;
        try {
            conn = await this.getConnection();

            const sql = `SELECT METRIC_ID, METRIC_NAME, METRIC_DEFINE, METRIC_NUMERATOR, METRIC_DENOMINATOR,
                         METRIC_CHAPTER, METRIC_ONE, METRIC_TWO, METRIC_THREE, METRIC_FOUR, DELETE_FLAG,
                         METRIC_DATA_SOURCE, METRIC_TYPE, METRIC_FORMULA, STANDARD_NAME, VERSION, PROVINCE,
                         CREATE_USER, CREATE_TIME, UPDATE_TIME
                         FROM METRIC WHERE METRIC_ID = :metricId AND DELETE_FLAG = 0`;

            const result = await conn.execute(sql, { metricId });

            if (result.rows.length === 0) {
                return {
                    success: false,
                    data: null,
                    message: '未找到指定的指标记录'
                };
            }

            // 转换结果为对象
            const metric = {};
            result.metaData.forEach((col, index) => {
                metric[col.name] = result.rows[0][index];
            });

            return {
                success: true,
                data: metric,
                message: '查询成功'
            };

        } catch (err) {
            console.log("查询失败：", err.message);
            return {
                success: false,
                data: null,
                message: `查询失败：${err.message}`
            };
        } finally {
            if (conn) await conn.close();
        }
    }

}

export default MetricTool;

