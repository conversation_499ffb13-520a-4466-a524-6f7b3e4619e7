import express from 'express';
const router = express.Router();
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { anthropic} from '../../utils/llm.js';
import LLMServerTool from '../../tools/LLMServerTool.js';
import MetricTool from '../../tools/MetricTool.js';
import Top3IndexImportRecordTool from '../../tools/Top3IndexImportRecordTool.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const llmServer = new LLMServerTool();
const top3IndexImportRecordTool = new Top3IndexImportRecordTool();
const metricTool = new MetricTool();
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// 确保目标文件夹存在
const uploadDir = path.join(__dirname, '../../resource/pdf');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置 multer 存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 保持原文件名
    cb(null, file.originalname);
  }
});
const upload = multer({ storage });

// 文件上传接口
router.post('/uploadByClaude', upload.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: '未上传文件' });
  }

  // 验证新增的必填参数
  const { standardName, version, province } = req.body;

  // 检查标准名称
  if (!standardName || standardName.trim() === '') {
    return res.status(400).json({
      success: false,
      message: '标准名称(standardName)不能为空!'
    });
  }

  // 检查版本
  if (!version || version.trim() === '') {
    return res.status(400).json({
      success: false,
      message: '版本(version)不能为空!'
    });
  }

  // 检查省份
  if (!province || province.trim() === '') {
    return res.status(400).json({
      success: false,
      message: '省份(province)不能为空!'
    });
  }
  res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  // 设置心跳定时器保持连接
  const heartbeatInterval = setInterval(() => {
    try {
      if (!res.writableEnded && !res.destroyed) {
        res.write(`: heartbeat\n\n`); // SSE注释行作为心跳
      } else {
        clearInterval(heartbeatInterval);
      }
    } catch (e) {
      clearInterval(heartbeatInterval);
    }
  }, 30000); // 每30秒发送一次心跳

  let responseText = '';
  try {
      const fileName = req.file.filename;
      
      let clientAborted = false;
      res.on('close', () => {
        clientAborted = true;
      });
      res.on('aborted', () => {
        clientAborted = true;
      });
      // 加载PDF文件，保留完整页面结构
      const pdfPages = await LoadFile(fileName);
      res.write(`data: ${JSON.stringify({content: `### PDF文件共**${pdfPages.length}**页\n\n`,})}\n\n`);
      
      // 分批次处理 PDF 页面
      // 每批处理 3-5 页，避免超出模型上下文窗口
      const pagesPerBatch = 1;
      const totalBatches = Math.ceil(pdfPages.length / pagesPerBatch);
      
      // 存储所有批次的提取结果
      const allMetrics = [];
      
      // 构建特定于表格提取的提示词
      const promptText = `
        分析这份PDF文档中的表格数据。从你接收到的文本中识别表格结构和行列关系。

        文本内容可能已经失去了原始的表格格式，所以请特别注意以下线索来识别表格：
        1. 短行且包含特定关键词的通常是表头或指标名
        2. 包含计算公式（如带有除法或乘100%）的行通常是指标的计算方法
        3. 提及"医院自行填报"的部分通常是数据来源
        4. 所属章节的内容通常是 "第几章 章节名称",不是第几部分, 如：第一章 资源配置与运行数据指标
        5. 只提取章节以下的作为标题，一级标题如： 一、床位配置 
        6. 注意计算分子、计算分母不能为空，为空的不提取
        7. 可能一个章节或一级标题跨越几张页面，注意识别文本的结构

        每个指标通常包含以下部分：
        - 编号和指标名称（如"2.1非瓣膜性心房颤动患者血栓栓塞风险评估率"）
        - 指标定义（通常是对指标的解释说明）
        - 计算分子（通常在分式的上方）
        - 计算分母（通常在分式的下方）
        - 数据来源（如"医院自行填报"）

        识别章节结构，如第三章 重点专业质量控制指标是所属章节，
        如"十四、心血管系统疾病相关专业医疗质量控制指标（2021 年版）"是一级标题，
        "（三）心力衰竭"是二级标题，其下属指标如"3.1心力衰竭患者入院24小时内利钠肽检测率"是该二级标题下的第一个指标。

        请输出严格的JSON数组格式，每个指标对应一个JSON对象，包含以下字段：
        {"METRIC_NAME": "指标名称", "METRIC_DEFINE": "指标定义","METRIC_NUMERATOR": "计算分子","METRIC_DENOMINATOR": "计算分母","METRIC_SOURCE": "数据来源","METRIC_CHAPTER": "所属章节","METRIC_ONE": "一级标题","METRIC_TWO": "二级标题","METRIC_THREE": "三级标题","METRIC_FOUR": "四级标题"}

        重要提示：
        1. 在未找到新章节或标题时，请沿用已知信息。
        2. 仅输出JSON数组，不包含任何额外说明或分析
        3. 使用markdown格式json包裹输出内容
        4. 确保JSON格式正确，不使用中文标点
        5. 如果没有找到任何指标，输出空数组 []
        6. 返回结果的所有字符串中的引号必须使用反斜杠转义（如 \\"引号内容\\"）
        `;
      
        // 修改处理流程，添加上下文跟踪
        let contextInfo = {
            currentChapter: "",
            currentLevelOne: "",
            currentLevelTwo: "",
            currentLevelThree: "",
            currentLevelFour: ""
        };

      // 分批处理文档页面
      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        res.write(`data: ${JSON.stringify({content: `\n\n ### 正在处理第**${batchIndex + 1}/${totalBatches}**批页面\n\n`,})}\n\n`);
        if (clientAborted) {
            console.log('客户端已中断连接，停止后续处理');
            break;
        }
         // 获取当前批次的页面，移除重叠处理
        const startIdx = batchIndex * pagesPerBatch;
        const endIdx = Math.min(startIdx + pagesPerBatch, pdfPages.length);
        const batchPages = pdfPages.slice(startIdx, endIdx);
        // 修改提示词，增加上下文传递
        const batchPrompt = `${promptText}
        
        当前分析的是第 ${startIdx + 1} 页到第 ${endIdx} 页的内容。
        上下文：章节="${contextInfo.currentChapter || ""}",一级="${contextInfo.currentLevelOne || ""}",二级="${contextInfo.currentLevelTwo || ""}",三级="${contextInfo.currentLevelThree || ""},四级="${contextInfo.currentLevelFour || ""}"
        
        请基于上述上下文信息，在未找到新的章节或标题时，沿用已知的章节和标题。特别关注表格结构并正确关联章节和标题信息。
        仅输出结果，不需要分析过程。`;
          
          // 构建大模型输入
          const messageWithPages = {
              role: "user",
              content: [
                  formatToAnthropicDocuments(batchPages),
                  {
                      type: "text",
                      text: batchPrompt,
                  },
              ],
          };
          
          // 调用Claude模型
        try {
            const response = await anthropic.invoke([messageWithPages], {
                max_tokens: 30000,  // 增加输出token限制
                temperature: 0.1, // 降低温度，使输出更确定性
                stream: false   
            });
            // console.log(`第 ${batchIndex + 1} 批响应获取成功`);
            
            // 处理 LangChain 格式的响应
            let responseText = '';
            // 检查响应格式并提取内容
            if (response && typeof response === 'object') {
                res.write(`data: ${JSON.stringify({
                    content: `\n\n ### 第 ${batchIndex + 1} 批响应获取成功:\n`+ response.content,
                })}\n\n`);
                
                // 检查是否为 AIMessage 格式
                if (response.content !== undefined) {
                    // 直接从 content 属性获取内容 (新的 AIMessage 格式)
                    if (typeof response.content === 'string') {
                        responseText = response.content;
                    } else if (Array.isArray(response.content) && response.content[0]) {
                        if (typeof response.content[0] === 'string') {
                            responseText = response.content[0];
                        } else if (response.content[0].text) {
                            responseText = response.content[0].text;
                        }
                    }
                } 
                // 检查旧的 LangChain 格式
                else if (response.kwargs && response.kwargs.content) {
                    responseText = response.kwargs.content;
                }
                
                // 如果以上方法都没提取到内容，查看是否有其他可能的内容位置
                if (!responseText && response.additional_kwargs) {
                    if (typeof response.additional_kwargs.content === 'string') {
                        responseText = response.additional_kwargs.content;
                    }
                }
                
                if (!responseText) {
                    res.write(`data: ${JSON.stringify({
                        content: `\n\n ### 第 ${batchIndex + 1} 批无法提取响应内容`,
                    })}\n\n`);
                    continue;
                }
            }
            
           // 尝试解析内容为 JSON
            let batchMetrics = [];
            
            // 改进的JSON解析
            try {
                // 首先尝试检测并处理可能的双重编码问题
                try {
                    // 如果responseText本身是一个JSON字符串，先解析它
                    const parsed = JSON.parse(responseText);
                    if (parsed.content && typeof parsed.content === 'string') {
                        responseText = parsed.content;
                    }
                } catch (e) {
                    // 不是双重编码，继续处理
                }
                
                // 删除控制字符并保留必要的空白
                responseText = responseText.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, "");
                
                // 如果是代码块，提取内容
                if (responseText.includes('```')) {
                    const codeBlockMatch = responseText.match(/```(?:json)?\s*([\s\S]*?)```/);
                    if (codeBlockMatch && codeBlockMatch[1]) {
                        responseText = codeBlockMatch[1].trim();
                    }
                }
                
                // 更全面的引号转义处理
                let enhancedEscapedResponseText = responseText
                    // 替换中文标点
                    .replace(/[，]/g, ',')
                    .replace(/[：]/g, ':')
                    .replace(/[""]|['']/g, '"')
                    // 处理特定模式中的引号
                    .replace(/记为"([^"]+)"/g, '记为\\"$1\\"')
                    .replace(/为"([^"]+)"/g, '为\\"$1\\"')
                    .replace(/注：([^"]*?)"([^"]+)"([^"]*)/g, '注：$1\\"$2\\"$3');
                
                // 更强大的引号转义正则表达式
                enhancedEscapedResponseText = enhancedEscapedResponseText
                    .replace(/: *"(.*?)([^\\])"(.*?)"/g, (match, before, quote, after) => {
                        return `: "${before}${quote}\\\"${after}"`;
                    });
                
                // 使用增强的文本进行解析
                if (enhancedEscapedResponseText.trim().startsWith('[') && enhancedEscapedResponseText.trim().endsWith(']')) {
                    batchMetrics = JSON.parse(enhancedEscapedResponseText.trim());
                    res.write(`data: ${JSON.stringify({
                        content: `\n\n #### 成功解析完整JSON数组，包含 ${batchMetrics.length} 个指标\n\n`,
                    })}\n\n`);
                } else {
                    // 尝试提取最外层的JSON数组
                    const arrayMatch = enhancedEscapedResponseText.match(/\[([\s\S]*?)\]/);
                    if (arrayMatch) {
                        // 重建有效的JSON数组
                        const jsonStr = `[${arrayMatch[1]}]`;
                        batchMetrics = JSON.parse(jsonStr);
                        res.write(`data: ${JSON.stringify({
                            content: `\n\n #####提取并重建JSON数组，包含 ${batchMetrics.length} 个指标\n\n`,
                        })}\n\n`);
                    } else {
                        // 尝试提取所有JSON对象
                        const objMatches = Array.from(escapedResponseText.matchAll(/\{[\s\S]*?\}/g));
                        if (objMatches && objMatches.length > 0) {
                            batchMetrics = [];
                            for (const match of objMatches) {
                                try {
                                    // 对每个对象单独处理未转义的引号
                                    const escapedObj = match[0].replace(/: *"([^"]*?)([^\\])"([^"]*?)"/g, (m, b, q, a) => {
                                        return `: "${b}${q}\\\"${a}"`;
                                    });
                                    const obj = JSON.parse(escapedObj);
                                    if (obj && obj.METRIC_NAME) {
                                        batchMetrics.push(obj);
                                    }
                                } catch (e) {
                                    // 忽略无法解析的对象
                                }
                            }
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n ##### 提取出 ${batchMetrics.length} 个独立JSON对象\n\n`,
                            })}\n\n`);
                        }
                    }
                }

                if(batchMetrics.length > 0){
                    //批量插入数据库
                    batchMetrics.map((metrics) => {
                        metrics.STANDARD_NAME = standardName;
                        metrics.VERSION = version;
                        metrics.PROVINCE = province;
                    });
                    const saveMessage = await metricTool.saveMetric(batchMetrics);
                    if(saveMessage){
                        const top3IndexImportRecord = {
                            province,
                            version,
                            standardName
                        };
                        //保存导入记录
                        await top3IndexImportRecordTool.save(top3IndexImportRecord);
                        res.write(`data: ${JSON.stringify({
                            content: `\n\n ##### 将提取的指标记录插入数据库， ${saveMessage} \n\n`,
                        })}\n\n`);
                    }
                }

            } catch (error) {
                console.log("JSON解析失败:",error);
                res.write(`data: ${JSON.stringify({
                    content: `\n\n #### JSON解析失败,进行二次解析！\n\n`,
                })}\n\n`);
                // 尝试处理常见的JSON格式问题
                try {
                    // 替换常见错误
                    let fixedText = responseText
                        .replace(/[""]|['']/g, '"')  // 替换各种引号
                        .replace(/[，]/g, ',')        // 替换中文逗号
                        .replace(/[：]/g, ':')        // 替换中文冒号
                        .replace(/(\w+)\s*:\s*"([^"]*?)([^\\])"([^"]*?)"/g, (m, key, before, quote, after) => {
                            // 处理值中的未转义引号
                            return `${key}: "${before}${quote}\\\"${after}"`;
                        });
                        
                    // 重新尝试解析
                    if (fixedText.trim().startsWith('[') && fixedText.trim().endsWith(']')) {
                        batchMetrics = JSON.parse(fixedText.trim());
                        res.write(`data: ${JSON.stringify({
                            content: `\n\n ##### 修复格式后成功解析JSON，包含 ${batchMetrics.length} 个指标 \n\n`,
                        })}\n\n`);
                        if(batchMetrics.length > 0){
                            //批量插入数据库
                            const saveMessage = await metricTool.saveMetric(batchMetrics);
                            if(saveMessage){
                                res.write(`data: ${JSON.stringify({
                                    content: `\n\n ##### 将提取的指标记录插入数据库， ${saveMessage} \n\n`,
                                })}\n\n`);
                            }
                        }
                    }
                } catch (fixError) {
                    // 如果上面的方法仍然失败，使用一个更保守的方法
                    try {
                        // 直接替换特定的问题模式（如"记为"多人次""这样的模式）
                        let specificFix = responseText
                            .replace(/记为"([^"]+)"/g, '记为\\"$1\\"')
                            .replace(/为"([^"]+)"/g, '为\\"$1\\"')
                            .replace(/"([^"]*?)([^\\])"([^"]*?)"/g, '"$1$2\\"$3"');
                            
                        if (specificFix.trim().startsWith('[') && specificFix.trim().endsWith(']')) {
                            batchMetrics = JSON.parse(specificFix.trim());
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n ####二次修复后成功解析JSON，包含 ${batchMetrics.length} 个指标\n\n`,
                            })}\n\n`);
                            if(batchMetrics.length > 0){
                                //批量插入数据库
                                const saveMessage = await metricTool.saveMetric(batchMetrics);
                                if(saveMessage){
                                    res.write(`data: ${JSON.stringify({
                                        content: `\n\n #### 将提取的指标记录插入数据库， ${saveMessage}\n\n`,
                                    })}\n\n`);
                                }
                            }
                        }
                    } catch (lastError) {
                        res.write(`data: ${JSON.stringify({
                            content: `\n\n #### 所有修复尝试均失败: ${lastError.message} \n\n`,
                        })}\n\n`);
                    }
                }
            }

            //连接中断即停止循环
            if (clientAborted) {
                console.log('\n\n #### 客户端已中断连接，停止后续处理 \n\n');
                break;
            }
            // 合并当前批次的结果
            if (Array.isArray(batchMetrics) && batchMetrics.length > 0) {
                allMetrics.push(...batchMetrics);
                res.write(`data: ${JSON.stringify({
                    content: `\n\n ##### 第 ${batchIndex + 1} 批提取了 ${batchMetrics.length} 个指标 \n\n`,
                })}\n\n`);
            } else {
                res.write(`data: ${JSON.stringify({
                    content: `\n\n ##### 第 ${batchIndex + 1} 批未提取到指标或指标格式不正确 \n\n`,
                })}\n\n`);
            }

            // 处理完成后，更新上下文信息
            if (Array.isArray(batchMetrics) && batchMetrics.length > 0) {
                // 找到最后一个有效的指标，更新上下文
                const lastMetric = batchMetrics[batchMetrics.length - 1];
                if (lastMetric.METRIC_CHAPTER) contextInfo.currentChapter = lastMetric.METRIC_CHAPTER;
                if (lastMetric.METRIC_ONE) contextInfo.currentLevelOne = lastMetric.METRIC_ONE;
                if (lastMetric.METRIC_TWO) contextInfo.currentLevelTwo = lastMetric.METRIC_TWO;
                if (lastMetric.METRIC_THREE) contextInfo.currentLevelThree = lastMetric.METRIC_THREE;
                if (lastMetric.METRIC_FOUR) contextInfo.currentLevelFour = lastMetric.METRIC_FOUR;
            }
        } catch (batchError) {
            res.write(`data: ${JSON.stringify({
                content: `\n\n ##### 处理第 ${batchIndex + 1} 批时出错: ${batchError.message} \n\n`,
            })}\n\n`);
        }
      }
      // 去重并整理结果
      const uniqueMetrics = [];
      const metricSet = new Set();
      
      for (const metric of allMetrics) {
          if (!metric || !metric.METRIC_NAME) continue;
          
          // 使用指标名称+章节作为唯一标识
          const key = `${metric.METRIC_NAME}|${metric.METRIC_CHAPTER}`;
          
          if (!metricSet.has(key)) {
              metricSet.add(key);
              uniqueMetrics.push(metric);
          }
      }
      
      res.write(`data: ${JSON.stringify({
        content: `\n\n #### 总共提取了 ${uniqueMetrics.length} 个计算指标`,
      })}\n\n`);

    // 在处理完成后清除心跳定时器
    clearInterval(heartbeatInterval);
        
    // 安全结束响应
    if (!res.writableEnded && !res.destroyed) {
        res.end();
    }
  } catch (error) {
    // 清除心跳定时器
    clearInterval(heartbeatInterval);
    
    console.log("解析失败：", error);
    // 如果响应还没结束，发送错误响应
    if (!res.writableEnded && !res.destroyed) {
      return res.status(500).json({ 
        success: false, 
        message: error.message || String(error),
        rawResponse: responseText
      });
    }
  }  
  
});

// 文件上传接口
router.post('/upload', upload.single('file'), async (req, res) => {
    if (!req.file) {
      return res.status(400).json({ success: false, message: '未上传文件' });
    }

    res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders();

        // 设置心跳定时器保持连接
    const heartbeatInterval = setInterval(() => {
        try {
        if (!res.writableEnded && !res.destroyed) {
            res.write(`: heartbeat\n\n`); // SSE注释行作为心跳
        } else {
            clearInterval(heartbeatInterval);
        }
        } catch (e) {
        clearInterval(heartbeatInterval);
        }
    }, 30000); // 每30秒发送一次心跳
    
    let responseText = '';
    try {
        const fileName = req.file.filename;

        //定义连接状态参数
        let clientAborted = false;
        res.on('close', () => {
            clientAborted = true;
        });
        res.on('aborted', () => {
            clientAborted = true;
        });
        // 加载PDF文件，保留完整页面结构
        const pdfPages = await LoadFile(fileName);
        res.write(`data: ${JSON.stringify({content: `### PDF文件共**${pdfPages.length}**页\n\n`,})}\n\n`);
        
        // 分批次处理 PDF 页面
        // 每批处理 3-5 页，避免超出模型上下文窗口
        const pagesPerBatch = 2;
        const totalBatches = Math.ceil(pdfPages.length / pagesPerBatch);
        
        // 存储所有批次的提取结果
        const allMetrics = [];
        
        // 构建特定于表格提取的提示词
        // 构建特定于表格提取的提示词
      const promptText = `
        分析这份PDF文档中的表格数据。从你接收到的文本中识别表格结构和行列关系。

        文本内容可能已经失去了原始的表格格式，所以请特别注意以下线索来识别表格：
        1. 短行且包含特定关键词的通常是表头或指标名
        2. 包含计算公式（如带有除法或乘100%）的行通常是指标的计算方法
        3. 提及"医院自行填报"的部分通常是数据来源
        4. 所属章节的内容通常是 "第几章 章节名称",不是第几部分, 如：第一章 资源配置与运行数据指标
        5. 只提取章节以下的作为标题，一级标题如： 一、床位配置 
        6. 注意计算分子、计算分母不能为空，为空的不提取
        7. 可能一个章节或一级标题跨越几张页面，注意识别文本的结构

        每个指标通常包含以下部分：
        - 编号和指标名称（如"2.1非瓣膜性心房颤动患者血栓栓塞风险评估率"）
        - 指标定义（通常是对指标的解释说明）
        - 计算分子（通常在分式的上方）
        - 计算分母（通常在分式的下方）
        - 数据来源（如"医院自行填报"）

        识别章节结构，如第三章 重点专业质量控制指标是所属章节，
        如"十四、心血管系统疾病相关专业医疗质量控制指标（2021 年版）"是一级标题，
        "（三）心力衰竭"是二级标题，其下属指标如"3.1心力衰竭患者入院24小时内利钠肽检测率"是该二级标题下的第一个指标。

        请输出严格的JSON数组格式，每个指标对应一个JSON对象，包含以下字段：
        {"METRIC_NAME": "指标名称", "METRIC_DEFINE": "指标定义","METRIC_NUMERATOR": "计算分子","METRIC_DENOMINATOR": "计算分母","METRIC_SOURCE": "数据来源","METRIC_CHAPTER": "所属章节","METRIC_ONE": "一级标题","METRIC_TWO": "二级标题","METRIC_THREE": "三级标题","METRIC_FOUR": "四级标题"}

        重要提示：
        1. 在未找到新章节或标题时，请沿用已知信息。
        2. 仅输出JSON数组，不包含任何额外说明或分析
        3. 使用markdown格式json包裹输出内容
        4. 确保JSON格式正确，不使用中文标点
        5. 如果没有找到任何指标，输出空数组 []
        6. 返回结果的所有字符串中的引号必须使用反斜杠转义（如 \\"引号内容\\"）
        `;

        // 修改处理流程，添加上下文跟踪
        let contextInfo = {
            currentChapter: "",
            currentLevelOne: "",
            currentLevelTwo: "",
            currentLevelThree: "",
            currentLevelFour: ""
        };

        
        // 分批处理文档页面
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            res.write(`data: ${JSON.stringify({content: `\n\n ### 正在处理第**${batchIndex + 1}/${totalBatches}**批页面\n\n`,})}\n\n`);
            if (clientAborted) {
                console.log('客户端已中断连接，停止后续处理');
                break;
            }
            
            // 获取当前批次的页面
            const startIdx = batchIndex * pagesPerBatch;
            const endIdx = Math.min(startIdx + pagesPerBatch, pdfPages.length);
            const batchPages = pdfPages.slice(startIdx, endIdx);
            
           // 修改提示词，增加上下文传递
            const batchPrompt = `${promptText}
            
            当前分析的是第 ${startIdx + 1} 页到第 ${endIdx} 页的内容。
            上下文：章节="${contextInfo.currentChapter || ""}",一级="${contextInfo.currentLevelOne || ""}",二级="${contextInfo.currentLevelTwo || ""}",三级="${contextInfo.currentLevelThree || ""},四级="${contextInfo.currentLevelFour || ""}"
            
            请基于上述上下文信息，在未找到新的章节或标题时，沿用已知的章节和标题。特别关注表格结构并正确关联章节和标题信息。
            仅输出结果，不需要分析过程。`;
            
            
            // 调用Kimi模型
            try {
                const pdfText = batchPages.map(p => p.content).join('\n');
                // 使用LLMServerTool调用DeepSeek API
                const responseText = await llmServer.extractTableFromPDFUsingKimi(pdfText, batchPrompt);
                res.write(`data: ${JSON.stringify({
                    content: `第 ${batchIndex + 1} 批响应获取成功:\n`+ responseText,
                })}\n\n`);
                
                // 尝试解析内容为 JSON
                let batchMetrics = [];
                
                  // 改进的JSON解析
                try {
                    let processedResponseText = responseText;
                        
                    // 如果是代码块，提取内容
                    if (processedResponseText.includes('```')) {
                        const codeBlockMatch = processedResponseText.match(/```(?:json)?\s*([\s\S]*?)```/);
                        if (codeBlockMatch && codeBlockMatch[1]) {
                            processedResponseText = codeBlockMatch[1].trim();
                        }
                    }
                    
                    // 更全面的引号转义处理
                    let enhancedEscapedResponseText = processedResponseText
                        // 替换中文标点
                        .replace(/[，]/g, ',')
                        .replace(/[：]/g, ':')
                        .replace(/[""]|['']/g, '"')
                        // 处理特定模式中的引号
                        .replace(/记为"([^"]+)"/g, '记为\\"$1\\"')
                        .replace(/为"([^"]+)"/g, '为\\"$1\\"')
                        .replace(/注：([^"]*?)"([^"]+)"([^"]*)/g, '注：$1\\"$2\\"$3');
                    
                    // 更强大的引号转义正则表达式
                    enhancedEscapedResponseText = enhancedEscapedResponseText
                        .replace(/: *"(.*?)([^\\])"(.*?)"/g, (match, before, quote, after) => {
                            return `: "${before}${quote}\\\"${after}"`;
                        });
                    
                    // 使用增强的文本进行解析
                    if (enhancedEscapedResponseText.trim().startsWith('[') && enhancedEscapedResponseText.trim().endsWith(']')) {
                        batchMetrics = JSON.parse(enhancedEscapedResponseText.trim());
                        res.write(`data: ${JSON.stringify({
                            content: `\n\n #### 成功解析完整JSON数组，包含 ${batchMetrics.length} 个指标\n\n`,
                        })}\n\n`);
                    } else {
                        // 尝试提取最外层的JSON数组
                        const arrayMatch = enhancedEscapedResponseText.match(/\[([\s\S]*?)\]/);
                        if (arrayMatch) {
                            // 重建有效的JSON数组
                            const jsonStr = `[${arrayMatch[1]}]`;
                            batchMetrics = JSON.parse(jsonStr);
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n #####提取并重建JSON数组，包含 ${batchMetrics.length} 个指标\n\n`,
                            })}\n\n`);
                        } else {
                            // 尝试提取所有JSON对象
                            const objMatches = Array.from(enhancedEscapedResponseText.matchAll(/\{[\s\S]*?\}/g));
                            if (objMatches && objMatches.length > 0) {
                                batchMetrics = [];
                                for (const match of objMatches) {
                                    try {
                                        // 对每个对象单独处理未转义的引号
                                        const escapedObj = match[0].replace(/: *"([^"]*?)([^\\])"([^"]*?)"/g, (m, b, q, a) => {
                                            return `: "${b}${q}\\\"${a}"`;
                                        });
                                        const obj = JSON.parse(escapedObj);
                                        if (obj && obj.METRIC_NAME) {
                                            batchMetrics.push(obj);
                                        }
                                    } catch (e) {
                                        // 忽略无法解析的对象
                                    }
                                }
                                res.write(`data: ${JSON.stringify({
                                    content: `\n\n ##### 提取出 ${batchMetrics.length} 个独立JSON对象\n\n`,
                                })}\n\n`);
                            }
                        }
                    }

                    if(batchMetrics.length > 0){
                        //批量插入数据库
                        const saveMessage = await metricTool.saveMetric(batchMetrics);
                        if(saveMessage){
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n ##### 将提取的指标记录插入数据库， ${saveMessage} \n\n`,
                            })}\n\n`);
                        }
                    }

                } catch (error) {
                    console.log("JSON解析失败:",error);
                    res.write(`data: ${JSON.stringify({
                        content: `\n\n #### JSON解析失败,进行二次解析！\n\n`,
                    })}\n\n`);
                    // 尝试处理常见的JSON格式问题
                    let batchMetrics = []; // 确保变量在此作用域中定义
                    try {
                        // 替换常见错误
                        let fixedText = responseText
                            .replace(/[""]|['']/g, '"')  // 替换各种引号
                            .replace(/[，]/g, ',')        // 替换中文逗号
                            .replace(/[：]/g, ':')        // 替换中文冒号
                            .replace(/(\w+)\s*:\s*"([^"]*?)([^\\])"([^"]*?)"/g, (m, key, before, quote, after) => {
                                // 处理值中的未转义引号
                                return `${key}: "${before}${quote}\\\"${after}"`;
                            });
                            
                        // 重新尝试解析
                        if (fixedText.trim().startsWith('[') && fixedText.trim().endsWith(']')) {
                            batchMetrics = JSON.parse(fixedText.trim());
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n ##### 修复格式后成功解析JSON，包含 ${batchMetrics.length} 个指标 \n\n`,
                            })}\n\n`);
                            if(batchMetrics.length > 0){
                                //批量插入数据库
                                const saveMessage = await metricTool.saveMetric(batchMetrics);
                                if(saveMessage){
                                    res.write(`data: ${JSON.stringify({
                                        content: `\n\n ##### 将提取的指标记录插入数据库， ${saveMessage} \n\n`,
                                    })}\n\n`);
                                }
                            }
                        }
                    } catch (fixError) {
                        // 如果上面的方法仍然失败，使用一个更保守的方法
                        try {
                            // 直接替换特定的问题模式（如"记为"多人次""这样的模式）
                            let specificFix = responseText
                                .replace(/记为"([^"]+)"/g, '记为\\"$1\\"')
                                .replace(/为"([^"]+)"/g, '为\\"$1\\"')
                                .replace(/"([^"]*?)([^\\])"([^"]*?)"/g, '"$1$2\\"$3"');
                                
                            if (specificFix.trim().startsWith('[') && specificFix.trim().endsWith(']')) {
                                batchMetrics = JSON.parse(specificFix.trim());
                                res.write(`data: ${JSON.stringify({
                                    content: `\n\n ####二次修复后成功解析JSON，包含 ${batchMetrics.length} 个指标\n\n`,
                                })}\n\n`);
                                if(batchMetrics.length > 0){
                                    //批量插入数据库
                                    const saveMessage = await metricTool.saveMetric(batchMetrics);
                                    if(saveMessage){
                                        res.write(`data: ${JSON.stringify({
                                            content: `\n\n #### 将提取的指标记录插入数据库， ${saveMessage}\n\n`,
                                        })}\n\n`);
                                    }
                                }
                            }
                        } catch (lastError) {
                            res.write(`data: ${JSON.stringify({
                                content: `\n\n #### 所有修复尝试均失败: ${lastError.message} \n\n`,
                            })}\n\n`);
                        }
                    }
                }

                //连接中断即停止循环
                if (clientAborted) {
                    console.log('\n\n #### 客户端已中断连接，停止后续处理 \n\n');
                    break;
                }
                // 合并当前批次的结果
                if (Array.isArray(batchMetrics) && batchMetrics.length > 0) {
                    allMetrics.push(...batchMetrics);
                    res.write(`data: ${JSON.stringify({
                        content: `\n\n ##### 第 ${batchIndex + 1} 批提取了 ${batchMetrics.length} 个指标 \n\n`,
                    })}\n\n`);
                } else {
                    res.write(`data: ${JSON.stringify({
                        content: `\n\n ##### 第 ${batchIndex + 1} 批未提取到指标或指标格式不正确 \n\n`,
                    })}\n\n`);
                }

                // 处理完成后，更新上下文信息
                if (Array.isArray(batchMetrics) && batchMetrics.length > 0) {
                    // 找到最后一个有效的指标，更新上下文
                    const lastMetric = batchMetrics[batchMetrics.length - 1];
                    if (lastMetric.METRIC_CHAPTER) contextInfo.currentChapter = lastMetric.METRIC_CHAPTER;
                    if (lastMetric.METRIC_ONE) contextInfo.currentLevelOne = lastMetric.METRIC_ONE;
                    if (lastMetric.METRIC_TWO) contextInfo.currentLevelTwo = lastMetric.METRIC_TWO;
                    if (lastMetric.METRIC_THREE) contextInfo.currentLevelThree = lastMetric.METRIC_THREE;
                    if (lastMetric.METRIC_FOUR) contextInfo.currentLevelFour = lastMetric.METRIC_FOUR;
                }
            } catch (batchError) {
                res.write(`data: ${JSON.stringify({
                    content: `\n\n ##### 处理第 ${batchIndex + 1} 批时出错: ${batchError.message} \n\n`,
                })}\n\n`);
            }
        }
        // 去重并整理结果
        const uniqueMetrics = [];
        const metricSet = new Set();
        
        for (const metric of allMetrics) {
            if (!metric || !metric.METRIC_NAME) continue;
            
            // 使用指标名称+章节作为唯一标识
            const key = `${metric.METRIC_NAME}|${metric.METRIC_CHAPTER}`;
            
            if (!metricSet.has(key)) {
                metricSet.add(key);
                uniqueMetrics.push(metric);
            }
        }
        
        res.write(`data: ${JSON.stringify({
            content: `\n\n #### 总共提取了 ${uniqueMetrics.length} 个计算指标`,
        })}\n\n`);

        // 在处理完成后清除心跳定时器
        clearInterval(heartbeatInterval);
            
        // 安全结束响应
        if (!res.writableEnded && !res.destroyed) {
            res.end();
        }
    } catch (error) {
        // 清除心跳定时器
        clearInterval(heartbeatInterval);
        
        console.log("解析失败：", error);
        // 如果响应还没结束，发送错误响应
        if (!res.writableEnded && !res.destroyed) {
        return res.status(500).json({ 
            success: false, 
            message: error.message || String(error),
            rawResponse: responseText
        });
    }
  }    
    
  });

// 更新 LoadFile 函数，以保留表格结构和章节信息
async function LoadFile(fileName) {
    const pdfPath = path.join(uploadDir, fileName);
    
    // 使用基本的PDFLoader加载
    const loader = new PDFLoader(pdfPath, { 
        splitPages: true
    });
    
    const rawPdfDocs = await loader.load();
    
    // 增强表格识别的文本处理
    const processedPages = rawPdfDocs.map((doc, i) => {
        // 在每行开头和结尾添加特殊标记，帮助识别表格行
        const enhancedContent = doc.pageContent
            .split('\n')
            .map(line => line.trim())
            .filter(line => line)
            .join('\n');
            
        return {
            pageNum: i + 1,
            content: enhancedContent,
            metadata: doc.metadata || {}
        };
    });
    return processedPages;
}

// 更新 formatToAnthropicDocuments 函数以适应新的数据格式
function formatToAnthropicDocuments(pages) {
    return {
        type: "document",
        source: {
            type: "content",
            content: pages.map((page) => ({ 
                type: "text", 
                text: `[Page ${page.pageNum}]\n${page.content}`
            })),
        },
        citations: { enabled: false },
    };
}



export default router;